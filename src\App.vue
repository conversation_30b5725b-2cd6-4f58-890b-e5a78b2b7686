<script setup lang="ts">
import { ref } from 'vue'

const isSidebarCollapsed = ref(false)

const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// Avue组件分类
const avueComponents = [
  { 
    name: 'Form 表单', 
    path: '/form',
    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />'
  },
  { 
    name: 'Crud 增删改查', 
    path: '/crud',
    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3zm0 5h16" />'
  },
  { 
    name: 'Default 默认组件', 
    path: '/default',
    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zm0 8a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zm12 0a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />'
  },
  { 
    name: 'Data 数据展示', 
    path: '/data',
    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />'
  },
  { 
    name: 'Component 组件', 
    path: '/component',
    icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 2v2m0 16v2m4-18v2m0 16v2M2 10h2m16 0h2M4 4l2 2m12 12l2 2M4 20l2-2m12-12l2-2" />'
  }
]
</script>

<template>
  <div class="flex flex-col h-screen w-screen overflow-hidden">
    <!-- Header -->
    <header class="bg-indigo-700 text-white shadow-lg z-10 flex-none">
      <div class="w-full px-4 py-3 flex items-center justify-between">
        <div class="flex items-center">
          <button @click="toggleSidebar" class="mr-3 text-white focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <h1 class="text-xl font-bold">Avue 组件示例</h1>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm">Avue 3.7.0</span>
          <div class="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center">
            <span class="text-xs font-bold">A</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content Area -->
    <div class="flex flex-1 overflow-hidden">
      <!-- Sidebar Navigation -->
      <aside 
        class="bg-gray-800 text-white shadow-lg transition-all duration-300 flex-none"
        :class="isSidebarCollapsed ? 'w-16' : 'w-64'"
      >
        <nav class="p-2 h-full overflow-y-auto">
          <div class="space-y-1">
            <RouterLink 
              to="/" 
              class="flex items-center px-3 py-2 rounded-md transition-colors hover:bg-indigo-600"
              :class="{ 'bg-indigo-700': $route.path === '/' }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              <span class="ml-3" v-show="!isSidebarCollapsed">首页</span>
            </RouterLink>
            
            <!-- Avue 组件导航 -->
            <div v-for="component in avueComponents" :key="component.path" class="mt-2">
              <RouterLink 
                :to="component.path" 
                class="flex items-center px-3 py-2 rounded-md transition-colors hover:bg-indigo-600"
                :class="{ 'bg-indigo-700': $route.path.startsWith(component.path) }"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" v-html="component.icon"></svg>
                <span class="ml-3" v-show="!isSidebarCollapsed">{{ component.name }}</span>
              </RouterLink>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Content Area -->
      <main class="flex-1 overflow-auto bg-gray-50">
        <div class="p-6 h-full">
          <RouterView />
        </div>
      </main>
    </div>
  </div>
</template>

<style>
/* 确保应用占满整个屏幕 */
html, body, #app {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
</style>

