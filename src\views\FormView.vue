<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Avue Form 表单组件</h1>
    
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
      <h2 class="text-lg font-semibold mb-4">基础表单示例</h2>
      <avue-form :option="option" v-model="form" @submit="handleSubmit"></avue-form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const form = ref({});
const option = ref({
  column: [
    {
      label: '姓名',
      prop: 'name',
      rules: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
      ]
    },
    {
      label: '年龄',
      prop: 'age',
      type: 'number',
      rules: [
        { required: true, message: '请输入年龄', trigger: 'blur' }
      ]
    },
    {
      label: '性别',
      prop: 'gender',
      type: 'select',
      dicData: [
        { label: '男', value: '1' },
        { label: '女', value: '0' }
      ],
      rules: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ]
    },
    {
      label: '爱好',
      prop: 'hobby',
      type: 'checkbox',
      dicData: [
        { label: '阅读', value: '1' },
        { label: '游泳', value: '2' },
        { label: '跑步', value: '3' },
        { label: '编程', value: '4' }
      ]
    },
    {
      label: '出生日期',
      prop: 'birthday',
      type: 'date'
    },
    {
      label: '个人简介',
      prop: 'intro',
      type: 'textarea'
    }
  ],
  submitBtn: true,
  submitText: '提交',
  resetBtn: true,
  resetText: '重置'
});

const handleSubmit = (form, done) => {
  console.log('表单提交数据:', form);
  setTimeout(() => {
    done();
    alert('提交成功');
  }, 1000);
};
</script>