<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Avue Crud 增删改查组件</h1>
    
    <div class="bg-white p-6 rounded-lg shadow-md">
      <avue-crud
        ref="crudRef"
        v-model="form"
        :option="option"
        :data="data"
        @row-save="rowSave"
        @row-update="rowUpdate"
        @row-del="rowDel"
      ></avue-crud>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const crudRef = ref(null);
const form = ref({});
const data = ref([
  {
    id: 1,
    name: '张三',
    age: 25,
    gender: '1',
    address: '北京市朝阳区',
    status: '1'
  },
  {
    id: 2,
    name: '李四',
    age: 30,
    gender: '1',
    address: '上海市浦东新区',
    status: '0'
  },
  {
    id: 3,
    name: '王五',
    age: 28,
    gender: '0',
    address: '广州市天河区',
    status: '1'
  }
]);

const option = ref({
  border: true,
  index: true,
  stripe: true,
  menuAlign: 'center',
  searchMenuSpan: 6,
  editBtn: true,
  delBtn: true,
  viewBtn: true,
  addBtn: true,
  column: [
    {
      label: 'ID',
      prop: 'id',
      editDisabled: true,
      addDisplay: false
    },
    {
      label: '姓名',
      prop: 'name',
      search: true,
      rules: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
      ]
    },
    {
      label: '年龄',
      prop: 'age',
      type: 'number'
    },
    {
      label: '性别',
      prop: 'gender',
      type: 'select',
      dicData: [
        { label: '男', value: '1' },
        { label: '女', value: '0' }
      ],
      search: true
    },
    {
      label: '地址',
      prop: 'address',
      type: 'textarea',
      span: 24
    },
    {
      label: '状态',
      prop: 'status',
      type: 'switch',
      dicData: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ],
      search: true
    }
  ]
});

// 新增
const rowSave = (row, done, loading) => {
  // 模拟API请求
  setTimeout(() => {
    row.id = data.value.length + 1;
    data.value.push(row);
    done();
    loading(false);
  }, 1000);
};

// 修改
const rowUpdate = (row, index, done, loading) => {
  // 模拟API请求
  setTimeout(() => {
    const idx = data.value.findIndex(item => item.id === row.id);
    if (idx !== -1) {
      data.value.splice(idx, 1, row);
    }
    done();
    loading(false);
  }, 1000);
};

// 删除
const rowDel = (row, index) => {
  // 模拟API请求
  const idx = data.value.findIndex(item => item.id === row.id);
  if (idx !== -1) {
    data.value.splice(idx, 1);
  }
};
</script>