<template>
  <avue-crud ref="crudRef"
             v-model="form"
             :option="option"
             @row-save="rowSave"
             @row-update="rowUpdate"
             @row-del="rowDel"
             :data="data"></avue-crud>
</template>
<script setup>
import { ref } from 'vue';
 
// 定义 ref 变量
const crudRef = ref(null);
const option = ref(null);
const data = ref([]);
const form = ref({});
 
// 配置 option
option.value = {
  index: true,
  addBtn: true, // 显示新增按钮
  editBtn: true, // 显示编辑按钮
  delBtn: true, // 显示删除按钮
  column: [
    {
      label: '姓名',
      prop: 'name',
      rules: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
      ]
    },
    {
      // 修正 prop 为 age
      label: '年龄',
      prop: 'age',
      rules: [
        { required: true, message: '请输入年龄', trigger: 'blur' },
        { type: Number, message: '年龄必须为数字值', trigger: 'blur' }
      ]
    }
  ]
};
 
// 初始化数据
data.value = [
  {
    id: 1,
    name: '张三',
    age: 12
  },
  {
    id: 2,
    name: '李四',
    age: 13
  }
];
 
// 新增保存方法
function rowSave(row, done, loading) {
  // 模拟生成新的 id
  row.id = Date.now();
  data.value.push(row);
  done();
  loading(false);
}
 
// 删除方法
function rowDel(row, index, done) {
  const currentIndex = data.value.findIndex(item => item.id === row.id);
  if (currentIndex!== -1) {
    data.value.splice(currentIndex, 1);
  }
  done();
}
 
// 编辑保存方法
function rowUpdate(row, index, done, loading) {
  const currentIndex = data.value.findIndex(item => item.id === row.id);
  if (currentIndex!== -1) {
    data.value[currentIndex] = { ...row };
  }
  done();
  loading(false);
}
</script>