<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Avue Default 默认组件</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 按钮组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">按钮组件</h2>
        <div class="space-y-4">
          <div class="flex space-x-2">
            <el-button type="primary">主要按钮</el-button>
            <el-button type="success">成功按钮</el-button>
            <el-button type="warning">警告按钮</el-button>
            <el-button type="danger">危险按钮</el-button>
            <el-button type="info">信息按钮</el-button>
          </div>
          <div class="flex space-x-2">
            <el-button type="primary" plain>朴素按钮</el-button>
            <el-button type="primary" round>圆角按钮</el-button>
            <el-button type="primary" icon="el-icon-search">图标按钮</el-button>
            <el-button type="primary" circle>圆</el-button>
          </div>
        </div>
      </div>
      
      <!-- 输入框组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">输入框组件</h2>
        <div class="space-y-4">
          <el-input v-model="input" placeholder="请输入内容"></el-input>
          <el-input v-model="textarea" type="textarea" placeholder="请输入多行内容"></el-input>
          <el-input-number v-model="num" :min="1" :max="10"></el-input-number>
        </div>
      </div>
      
      <!-- 选择器组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">选择器组件</h2>
        <div class="space-y-4">
          <el-select v-model="select" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          
          <el-cascader v-model="cascader" :options="cascaderOptions"></el-cascader>
          
          <el-date-picker v-model="date" type="date" placeholder="选择日期"></el-date-picker>
        </div>
      </div>
      
      <!-- 消息提示 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">消息提示</h2>
        <div class="space-y-4">
          <el-button @click="openMessage('success')">成功消息</el-button>
          <el-button @click="openMessage('warning')">警告消息</el-button>
          <el-button @click="openMessage('error')">错误消息</el-button>
          <el-button @click="openNotification">通知</el-button>
          <el-button @click="openAlert">警告框</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';

// 输入框数据
const input = ref('');
const textarea = ref('');
const num = ref(1);

// 选择器数据
const select = ref('');
const options = ref([
  { value: '1', label: '选项1' },
  { value: '2', label: '选项2' },
  { value: '3', label: '选项3' }
]);

const cascader = ref([]);
const cascaderOptions = ref([
  {
    value: 'zhinan',
    label: '指南',
    children: [
      { value: 'shejiyuanze', label: '设计原则' },
      { value: 'daohang', label: '导航' }
    ]
  },
  {
    value: 'zujian',
    label: '组件',
    children: [
      { value: 'basic', label: '基础组件' },
      { value: 'form', label: '表单组件' }
    ]
  }
]);

const date = ref('');

// 消息提示方法
const openMessage = (type) => {
  ElMessage({
    type,
    message: `这是一条${type === 'success' ? '成功' : type === 'warning' ? '警告' : '错误'}消息`
  });
};

const openNotification = () => {
  ElNotification({
    title: '通知',
    message: '这是一条通知消息',
    type: 'info'
  });
};

const openAlert = () => {
  ElMessageBox.alert('这是一条警告消息', '提示', {
    confirmButtonText: '确定',
    type: 'warning'
  });
};
</script>