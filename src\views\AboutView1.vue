<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow">
      <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <img class="block h-8 w-auto" src="https://tailwindui.com/img/logos/workflow-mark-indigo-600.svg" alt="Workflow">
            </div>
            <div class="hidden sm:ml-6 sm:flex sm:items-center">
              <div class="ml-4 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                Projects
              </div>
              <div class="ml-4 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                Calendar
              </div>
              <div class="ml-4 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                Team
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              New Project
            </button>
          </div>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Hero Section -->
      <section class="py-10">
        <div class="text-center">
          <h1 class="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
            Build amazing things
          </h1>
          <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-5 sm:text-2xl">
            From landing pages to complex web applications, we provide the tools you need to build your next project.
          </p>
          <div class="mt-5 max-w-xl mx-auto">
            <button type="button" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              Get started
            </button>
          </div>
        </div>
      </section>

      <!-- Carousel Section -->
      <section class="mt-10">
        <div class="relative">
          <div class="flex overflow-x-auto space-x-4 px-4 pb-4 -mx-4 touch-scroll">
            <div
              v-for="(project, index) in projects"
              :key="index"
              class="flex-shrink-0 w-64 h-40 bg-gray-200 rounded-lg overflow-hidden transform transition-transform duration-300 hover:scale-105"
            >
              <img :src="project.image" :alt="project.name" class="w-full h-full object-cover">
              <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <div class="text-white text-center">
                  <h3 class="text-xl font-bold">{{ project.name }}</h3>
                  <p class="text-sm">{{ project.description }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="flex justify-center space-x-2 mt-4">
            <button
              v-for="i in projects.length"
              :key="i"
              class="w-3 h-3 rounded-full bg-gray-400"
              :class="{ 'bg-indigo-600': activeSlide === i-1 }"
              @click="activeSlide = i-1"
            ></button>
          </div>
        </div>
      </section>

      <!-- Products Section -->
      <section class="mt-10">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Featured Products</h2>
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="product in products"
            :key="product.id"
            class="bg-white rounded-lg shadow overflow-hidden transform transition-transform duration-300 hover:scale-105"
          >
            <img :src="product.image" :alt="product.name" class="w-full h-48 object-cover">
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900">{{ product.name }}</h3>
              <p class="mt-2 text-base text-gray-600">{{ product.description }}</p>
              <div class="mt-4">
                <button
                  @click="showProductDetails(product)"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Learn more
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Product Details Modal -->
      <teleport to="body">
        <div v-if="selectedProduct" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center">
          <div class="bg-white rounded-lg w-full max-w-2xl p-6 transform transition-transform duration-300">
            <div class="flex items-center justify-between">
              <h3 class="text-2xl font-bold">{{ selectedProduct.name }}</h3>
              <button @click="closeModal" class="text-gray-400 hover:text-gray-500">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <img :src="selectedProduct.image" :alt="selectedProduct.name" class="w-full h-64 object-cover mt-4 rounded-lg">
            <div class="mt-4">
              <p class="text-gray-600">{{ selectedProduct.description }}</p>
              <div class="mt-4">
                <button @click="closeModal" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition">
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      </teleport>
    </main>

    <!-- Footer -->
    <footer class="bg-white">
      <div class="max-w-7xl mx-auto py-12 px-4 overflow-hidden sm:px-6 lg:px-8">
        <p class="text-sm text-gray-500 text-center">
          &copy; 2025 Your Company. All rights reserved.
        </p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

// Data
const projects = [
  {
    name: 'Project One',
    description: 'A landing page for a SaaS product',
    image: 'https://source.unsplash.com/random/600x400/?tech'
  },
  {
    name: 'Project Two',
    description: 'An e-commerce platform',
    image: 'https://source.unsplash.com/random/600x400/?shopping'
  },
  {
    name: 'Project Three',
    description: 'A blog system',
    image: 'https://source.unsplash.com/random/600x400/?blog'
  },
  {
    name: 'Project Four',
    description: 'A dashboard interface',
    image: 'https://source.unsplash.com/random/600x400/?dashboard'
  }
];

const products = [
  {
    id: 1,
    name: 'Product A',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.',
    image: 'https://source.unsplash.com/random/600x400/?product1'
  },
  {
    id: 2,
    name: 'Product B',
    description: 'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
    image: 'https://source.unsplash.com/random/600x400/?product2'
  },
  {
    id: 3,
    name: 'Product C',
    description: 'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
    image: 'https://source.unsplash.com/random/600x400/?product3'
  },
  {
    id: 4,
    name: 'Product D',
    description: 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.',
    image: 'https://source.unsplash.com/random/600x400/?product4'
  },
  {
    id: 5,
    name: 'Product E',
    description: 'Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos.',
    image: 'https://source.unsplash.com/random/600x400/?product5'
  },
  {
    id: 6,
    name: 'Product F',
    description: 'Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.',
    image: 'https://source.unsplash.com/random/600x400/?product6'
  }
];

// State
const activeSlide = ref(0);
const selectedProduct = ref(null);

// Methods
const showProductDetails = (product) => {
  selectedProduct.value = product;
};

const closeModal = () => {
  selectedProduct.value = null;
};

// Watchers
watch(activeSlide, (newValue) => {
  const carousel = document.querySelector('.touch-scroll');
  if (carousel) {
    carousel.scrollTo({
      left: newValue * 240,
      behavior: 'smooth'
    });
  }
});
</script>

<style scoped>
/* Add any custom styles here */
</style>