{"name": "my-vue3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@smallwei/avue": "^3.7.0", "@tailwindcss/vite": "^4.1.10", "axios": "^1.10.0", "element-plus": "^2.10.2", "modern-normalize": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@types/smallwei__avue": "^3.0.5", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "npm-run-all2": "^7.0.2", "postcss": "^8.5.6", "prettier": "3.5.3", "tailwindcss": "^4.1.10", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^2.2.8"}}