<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Avue Data 数据展示组件</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 表格组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md col-span-2">
        <h2 class="text-lg font-semibold mb-4">表格组件</h2>
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="date" label="日期" width="180"></el-table-column>
          <el-table-column prop="name" label="姓名" width="180"></el-table-column>
          <el-table-column prop="address" label="地址"></el-table-column>
        </el-table>
      </div>
      
      <!-- 标签页组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">标签页组件</h2>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="用户管理" name="first">用户管理内容</el-tab-pane>
          <el-tab-pane label="配置管理" name="second">配置管理内容</el-tab-pane>
          <el-tab-pane label="角色管理" name="third">角色管理内容</el-tab-pane>
          <el-tab-pane label="定时任务" name="fourth">定时任务内容</el-tab-pane>
        </el-tabs>
      </div>
      
      <!-- 分页组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">分页组件</h2>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="100"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      
      <!-- 树形控件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">树形控件</h2>
        <el-tree :data="treeData" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
      </div>
      
      <!-- 进度条 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">进度条</h2>
        <el-progress :percentage="50"></el-progress>
        <el-progress :percentage="80" color="#8e71c7"></el-progress>
        <el-progress :percentage="100" status="success"></el-progress>
        <el-progress :percentage="50" status="exception"></el-progress>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 表格数据
const tableData = ref([
  {
    date: '2023-05-03',
    name: '张三',
    address: '北京市朝阳区芍药居'
  },
  {
    date: '2023-05-02',
    name: '李四',
    address: '北京市海淀区西二旗'
  },
  {
    date: '2023-05-01',
    name: '王五',
    address: '上海市浦东新区金桥'
  },
  {
    date: '2023-04-30',
    name: '赵六',
    address: '广州市天河区珠江新城'
  }
]);

// 标签页
const activeTab = ref('first');

// 分页
const handleCurrentChange = (val) => {
  console.log(`当前页: ${val}`);
};

// 树形控件
const treeData = ref([
  {
    label: '一级 1',
    children: [
      {
        label: '二级 1-1',
        children: [
          { label: '三级 1-1-1' }
        ]
      }
    ]
  },
  {
    label: '一级 2',
    children: [
      {
        label: '二级 2-1',
        children: [
          { label: '三级 2-1-1' }
        ]
      },
      {
        label: '二级 2-2',
        children: [
          { label: '三级 2-2-1' }
        ]
      }
    ]
  }
]);

const defaultProps = {
  children: 'children',
  label: 'label'
};

const handleNodeClick = (data) => {
  console.log(data);
};
</script>