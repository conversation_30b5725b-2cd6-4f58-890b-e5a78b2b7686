import './assets/main.css'
import 'modern-normalize/modern-normalize.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

// 引入ElementPlus
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
// 引入 Avue 组件库
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
// 引入axios
import axios from 'axios'


const app = createApp(App)

app.use(router)
app.use(ElementPlus);
app.use(Avue, { axios })

app.mount('#app')

