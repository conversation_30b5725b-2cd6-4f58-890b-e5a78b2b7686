<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">Avue Component 组件</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 上传组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">上传组件</h2>
        <el-upload
          class="upload-demo"
          action="https://jsonplaceholder.typicode.com/posts/"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          multiple
          :limit="3"
          :on-exceed="handleExceed"
        >
          <el-button type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传jpg/png文件，且不超过500kb
            </div>
          </template>
        </el-upload>
      </div>
      
      <!-- 评分组件 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">评分组件</h2>
        <div class="block">
          <span class="demonstration">默认不区分颜色</span>
          <el-rate v-model="value1"></el-rate>
        </div>
        <div class="block">
          <span class="demonstration">区分颜色</span>
          <el-rate
            v-model="value2"
            :colors="colors"
          ></el-rate>
        </div>
      </div>
      
      <!-- 颜色选择器 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">颜色选择器</h2>
        <el-color-picker v-model="color1"></el-color-picker>
        <el-color-picker v-model="color2" show-alpha></el-color-picker>
      </div>
      
      <!-- 时间选择器 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">时间选择器</h2>
        <el-time-picker
          v-model="time"
          placeholder="选择时间"
          style="width: 100%;"
        ></el-time-picker>
      </div>
      
      <!-- 滑块 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">滑块</h2>
        <el-slider v-model="slider1"></el-slider>
        <el-slider
          v-model="slider2"
          range
          show-stops
          :max="10"
        ></el-slider>
      </div>
      
      <!-- 开关 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">开关</h2>
        <div class="flex items-center space-x-4">
          <el-switch v-model="switch1"></el-switch>
          <el-switch
            v-model="switch2"
            active-color="#13ce66"
            inactive-color="#ff4949"
          ></el-switch>
        </div>
      </div>
      
      <!-- 日期选择器 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">日期选择器</h2>
        <el-date-picker
          v-model="date"
          type="date"
          placeholder="选择日期"
          style="width: 100%;"
        ></el-date-picker>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessageBox } from 'element-plus';

// 上传组件方法
const handleRemove = (file, fileList) => {
  console.log(file, fileList);
};

const handlePreview = (file) => {
  console.log(file);
};

const handleExceed = (files, fileList) => {
  ElMessageBox.alert(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`, '提示');
};

const beforeRemove = (file) => {
  return ElMessageBox.confirm(`确定移除 ${file.name}？`);
};

// 评分组件数据
const value1 = ref(null);
const value2 = ref(null);
const colors = ref(['#99A9BF', '#F7BA2A', '#FF9900']);

// 颜色选择器数据
const color1 = ref('#409EFF');
const color2 = ref('rgba(19, 206, 102, 0.8)');

// 时间选择器数据
const time = ref('');

// 滑块数据
const slider1 = ref(50);
const slider2 = ref([20, 80]);

// 开关数据
const switch1 = ref(true);
const switch2 = ref(false);

// 日期选择器数据
const date = ref('');
</script>
